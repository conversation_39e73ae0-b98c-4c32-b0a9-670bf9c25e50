"use client";

import React from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface CPORevenueChartProps {
  monthlyData: {
    month: string;
    recurringRevenue: number;
  }[];
  yearForecast: number;
  onboardingRevenue: number;
}

const CPORevenueChart = ({ monthlyData, yearForecast, onboardingRevenue }: CPORevenueChartProps) => {
  const formatMonth = (monthKey: string) => {
    const [year, month] = monthKey.split("-");
    const monthNames = [
      "Jan", "Feb", "Mär", "Apr", "Mai", "Jun",
      "Jul", "Aug", "Sep", "Okt", "Nov", "Dez"
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  // Prepare data for chart
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;
  
  // Get all months for current year
  const allMonths = [];
  for (let i = 1; i <= 12; i++) {
    allMonths.push(`${currentYear}-${String(i).padStart(2, "0")}`);
  }

  // Map actual data and forecast
  const chartData = allMonths.map(month => {
    const monthNum = parseInt(month.split("-")[1]);
    const existingData = monthlyData.find(d => d.month === month);
    
    if (existingData) {
      return existingData.recurringRevenue;
    } else if (monthNum > currentMonth) {
      // Forecast for future months
      const avgMonthlyRecurring = monthlyData.length > 0 
        ? monthlyData.reduce((sum, d) => sum + d.recurringRevenue, 0) / monthlyData.length
        : 0;
      return avgMonthlyRecurring;
    } else {
      return 0;
    }
  });

  const data = {
    labels: allMonths.map(formatMonth),
    datasets: [
      {
        label: "Wiederkehrende Einnahmen",
        data: chartData,
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.1)",
        tension: 0.1,
        pointBackgroundColor: allMonths.map((_, index) => 
          index < currentMonth ? "rgb(59, 130, 246)" : "rgba(59, 130, 246, 0.5)"
        ),
        pointBorderColor: allMonths.map((_, index) => 
          index < currentMonth ? "rgb(59, 130, 246)" : "rgba(59, 130, 246, 0.5)"
        ),
        borderDash: allMonths.map((_, index) => 
          index >= currentMonth ? [5, 5] : []
        ),
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
      },
      title: {
        display: true,
        text: `Monatliche wiederkehrende Einnahmen (Prognose: ${new Intl.NumberFormat("de-DE", {
          style: "currency",
          currency: "EUR",
        }).format(yearForecast)})`,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value: any) {
            return new Intl.NumberFormat("de-DE", {
              style: "currency",
              currency: "EUR",
              minimumFractionDigits: 0,
            }).format(value);
          },
        },
      },
    },
  };

  return (
    <div className="h-64">
      <Line data={data} options={options} />
    </div>
  );
};

export default CPORevenueChart;
