"use client";

import React from "react";
import ReactECharts from "echarts-for-react";

interface CPORevenueChartProps {
  monthlyData: {
    month: string;
    recurringRevenue: number;
  }[];
  yearForecast: number;
  onboardingRevenue: number;
}

const CPORevenueChart = ({ monthlyData, yearForecast, onboardingRevenue }: CPORevenueChartProps) => {
  const formatMonth = (monthKey: string) => {
    const [year, month] = monthKey.split("-");
    const monthNames = [
      "Jan", "Feb", "Mär", "Apr", "Mai", "Jun",
      "Jul", "Aug", "Sep", "Okt", "Nov", "Dez"
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("de-DE", {
      style: "currency",
      currency: "EUR",
      minimumFractionDigits: 0,
    }).format(value);
  };

  // Prepare data for chart
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;

  // Get all months for current year
  const allMonths = [];
  for (let i = 1; i <= 12; i++) {
    allMonths.push(`${currentYear}-${String(i).padStart(2, "0")}`);
  }

  // Calculate linear trend line using least squares method
  const calculateTrendLine = (data: { x: number; y: number }[]) => {
    if (data.length < 2) return { slope: 0, intercept: 0 };

    const n = data.length;
    const sumX = data.reduce((sum, point) => sum + point.x, 0);
    const sumY = data.reduce((sum, point) => sum + point.y, 0);
    const sumXY = data.reduce((sum, point) => sum + point.x * point.y, 0);
    const sumXX = data.reduce((sum, point) => sum + point.x * point.x, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    return { slope, intercept };
  };

  // Prepare data for trend calculation
  const validMonthlyData = monthlyData.filter(d => d.recurringRevenue > 0);
  const trendData = validMonthlyData.map((d, index) => ({
    x: index + 1, // Month position (1, 2, 3, ...)
    y: d.recurringRevenue
  }));

  const { slope, intercept } = calculateTrendLine(trendData);

  // Map actual data and trend line
  const actualData: (number | null)[] = [];
  const trendLineData: (number | null)[] = [];

  let actualDataCount = 0;

  allMonths.forEach((month, index) => {
    const monthNum = parseInt(month.split("-")[1]);
    const existingData = monthlyData.find(d => d.month === month);

    if (existingData && existingData.recurringRevenue > 0) {
      actualData.push(existingData.recurringRevenue);
      actualDataCount++;
      // Calculate trend value for this position
      const trendValue = slope * actualDataCount + intercept;
      trendLineData.push(Math.max(0, trendValue)); // Ensure non-negative
    } else if (monthNum > currentMonth && trendData.length >= 2) {
      // Extend trend line into future months
      actualData.push(null);
      actualDataCount++;
      const trendValue = slope * actualDataCount + intercept;
      trendLineData.push(Math.max(0, trendValue)); // Ensure non-negative
    } else if (monthNum === currentMonth && (!existingData || existingData.recurringRevenue === 0)) {
      // Skip current month if it has no data (0)
      actualData.push(null);
      trendLineData.push(null);
    } else {
      actualData.push(existingData ? existingData.recurringRevenue : 0);
      trendLineData.push(null);
    }
  });

  const options = {
    title: {
      text: `Wiederkehrende Einnahmen & Jahresprognose (${formatCurrency(yearForecast)})`,
      left: "center",
      textStyle: {
        fontSize: 14,
        fontWeight: "bold",
      },
    },
    tooltip: {
      trigger: "axis",
      formatter: function(params: any) {
        let result = `<strong>${params[0].axisValue}</strong><br/>`;
        params.forEach((param: any) => {
          if (param.value !== null) {
            result += `${param.seriesName}: ${formatCurrency(param.value)}<br/>`;
          }
        });
        return result;
      },
    },
    legend: {
      data: ["Ist-Werte", "Prognose"],
      top: 30,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: allMonths.map(formatMonth),
      axisLabel: {
        rotate: 45,
        fontSize: 10,
      },
    },
    yAxis: {
      type: "value",
      name: "Einnahmen (€)",
      nameLocation: "middle",
      nameGap: 50,
      axisLabel: {
        formatter: function(value: number) {
          return formatCurrency(value);
        },
        fontSize: 10,
      },
    },
    series: [
      {
        name: "Ist-Werte",
        type: "line",
        data: actualData,
        lineStyle: {
          color: "#3b82f6",
          width: 3,
        },
        itemStyle: {
          color: "#3b82f6",
        },
        symbol: "circle",
        symbolSize: 6,
        connectNulls: false,
      },
      {
        name: "Prognose",
        type: "line",
        data: forecastData,
        lineStyle: {
          color: "#f59e0b",
          width: 2,
          type: "dashed",
        },
        itemStyle: {
          color: "#f59e0b",
        },
        symbol: "diamond",
        symbolSize: 6,
        connectNulls: false,
      },
    ],
  };

  return (
    <div className="h-64">
      <ReactECharts option={options} style={{ height: "100%", width: "100%" }} />
    </div>
  );
};

export default CPORevenueChart;
