"use client";

import React, { useState, useEffect } from "react";
import Card from "~/component/card";
import But<PERSON> from "~/component/button";
import { FaSearch, FaDownload, FaTrendingUp, FaTrendingDown, FaDollarSign, FaFileText } from "react-icons/fa";
import type { CPORevenueDashboardData, CPORevenueData } from "~/app/api/finance-dashboard/route";

const CPORevenueDashboard = () => {
  const [data, setData] = useState<CPORevenueDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredCpos, setFilteredCpos] = useState<CPORevenueData[]>([]);

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (data) {
      const filtered = data.cpos.filter(cpo =>
        cpo.cpoName.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredCpos(filtered);
    }
  }, [data, searchTerm]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/cpo-revenue-dashboard");
      if (response.ok) {
        const result = await response.json();
        setData(result);
      } else {
        console.error("Failed to fetch CPO revenue data");
      }
    } catch (error) {
      console.error("Error fetching CPO revenue data:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("de-DE", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  const exportToCSV = () => {
    if (!data) return;

    const headers = [
      "CPO Name",
      "Gesamtumsatz",
      "Gutschriften",
      "Rechnungen",
      "Onboarding",
      "Wiederkehrend",
      "Jahresprognose"
    ];

    const csvData = filteredCpos.map(cpo => [
      cpo.cpoName,
      cpo.totalRevenue.toFixed(2),
      cpo.totalCreditNotes.toFixed(2),
      cpo.totalInvoiced.toFixed(2),
      cpo.onboardingRevenue.toFixed(2),
      cpo.recurringRevenue.toFixed(2),
      cpo.yearForecast.toFixed(2)
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", `cpo-revenue-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center text-gray-500 dark:text-gray-400">
        Fehler beim Laden der CPO-Umsatzdaten
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Gesamtumsatz</p>
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(data.totalRevenue)}
              </p>
            </div>
            <FaDollarSign className="h-8 w-8 text-green-500" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Gutschriften</p>
              <p className="text-2xl font-bold text-red-600">
                {formatCurrency(data.totalCreditNotes)}
              </p>
            </div>
            <FaTrendingDown className="h-8 w-8 text-red-500" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Rechnungen</p>
              <p className="text-2xl font-bold text-blue-600">
                {formatCurrency(data.totalInvoiced)}
              </p>
            </div>
            <FaFileText className="h-8 w-8 text-blue-500" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Onboarding</p>
              <p className="text-2xl font-bold text-purple-600">
                {formatCurrency(data.totalOnboarding)}
              </p>
            </div>
            <FaTrendingUp className="h-8 w-8 text-purple-500" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Wiederkehrend</p>
              <p className="text-2xl font-bold text-orange-600">
                {formatCurrency(data.totalRecurring)}
              </p>
            </div>
            <FaTrendingUp className="h-8 w-8 text-orange-500" />
          </div>
        </Card>
      </div>

      {/* Filters and Actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="relative flex-1 max-w-sm">
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="CPO suchen..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="input pl-10 w-full"
          />
        </div>

        <div className="flex gap-2 items-center">
          <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
            {filteredCpos.length} von {data.cpos.length} CPOs
          </span>
          <Button onClick={exportToCSV} className="flex items-center gap-2">
            <FaDownload className="h-4 w-4" />
            CSV Export
          </Button>
        </div>
      </div>

      {/* CPO Revenue Table */}
      <Card header_left="CPO Details">
        <div className="p-4">
          {filteredCpos.length === 0 ? (
            <p className="text-center text-gray-500">Keine CPO-Daten gefunden</p>
          ) : (
            <div className="space-y-4">
              {filteredCpos.map((cpo) => (
                <div key={cpo.cpoId} className="border rounded-lg p-4">
                  <h3 className="font-semibold text-lg">{cpo.cpoName}</h3>
                  <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mt-2 text-sm">
                    <div>
                      <span className="text-gray-500">Gesamtumsatz:</span>
                      <p className="font-semibold text-green-600">{formatCurrency(cpo.totalRevenue)}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Gutschriften:</span>
                      <p className="font-semibold text-red-600">{formatCurrency(cpo.totalCreditNotes)}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Rechnungen:</span>
                      <p className="font-semibold text-blue-600">{formatCurrency(cpo.totalInvoiced)}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Onboarding:</span>
                      <p className="font-semibold text-purple-600">{formatCurrency(cpo.onboardingRevenue)}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Wiederkehrend:</span>
                      <p className="font-semibold text-orange-600">{formatCurrency(cpo.recurringRevenue)}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Jahresprognose:</span>
                      <p className="font-semibold text-green-600">{formatCurrency(cpo.yearForecast)}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default CPORevenueDashboard;
