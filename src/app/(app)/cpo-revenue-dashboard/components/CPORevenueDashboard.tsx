"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "~/component/ui/card";
import { <PERSON><PERSON> } from "~/component/ui/button";
import { Input } from "~/component/ui/input";
import { Badge } from "~/component/ui/badge";
import { Search, Download, TrendingUp, TrendingDown, DollarSign, FileText } from "lucide-react";
import type { CPORevenueDashboardData, CPORevenueData } from "~/app/api/finance-dashboard/route";
import CPORevenueTable from "./CPORevenueTable";

const CPORevenueDashboard = () => {
  const [data, setData] = useState<CPORevenueDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredCpos, setFilteredCpos] = useState<CPORevenueData[]>([]);

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (data) {
      const filtered = data.cpos.filter(cpo =>
        cpo.cpoName.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredCpos(filtered);
    }
  }, [data, searchTerm]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/cpo-revenue-dashboard");
      if (response.ok) {
        const result = await response.json();
        setData(result);
      } else {
        console.error("Failed to fetch CPO revenue data");
      }
    } catch (error) {
      console.error("Error fetching CPO revenue data:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("de-DE", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  const exportToCSV = () => {
    if (!data) return;

    const headers = [
      "CPO Name",
      "Gesamtumsatz",
      "Gutschriften",
      "Rechnungen",
      "Onboarding",
      "Wiederkehrend",
      "Jahresprognose"
    ];

    const csvData = filteredCpos.map(cpo => [
      cpo.cpoName,
      cpo.totalRevenue.toFixed(2),
      cpo.totalCreditNotes.toFixed(2),
      cpo.totalInvoiced.toFixed(2),
      cpo.onboardingRevenue.toFixed(2),
      cpo.recurringRevenue.toFixed(2),
      cpo.yearForecast.toFixed(2)
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", `cpo-revenue-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center text-gray-500 dark:text-gray-400">
        Fehler beim Laden der CPO-Umsatzdaten
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gesamtumsatz</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(data.totalRevenue)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gutschriften</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {formatCurrency(data.totalCreditNotes)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rechnungen</CardTitle>
            <FileText className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(data.totalInvoiced)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Onboarding</CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {formatCurrency(data.totalOnboarding)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Wiederkehrend</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {formatCurrency(data.totalRecurring)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="CPO suchen..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <Badge variant="outline" className="text-sm">
            {filteredCpos.length} von {data.cpos.length} CPOs
          </Badge>
          <Button onClick={exportToCSV} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            CSV Export
          </Button>
        </div>
      </div>

      {/* CPO Revenue Table */}
      <CPORevenueTable cpos={filteredCpos} />
    </div>
  );
};

export default CPORevenueDashboard;
