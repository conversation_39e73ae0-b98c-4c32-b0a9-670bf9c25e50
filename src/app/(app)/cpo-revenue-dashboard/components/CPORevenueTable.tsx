"use client";

import React, { useState } from "react";
import Card from "~/component/card";
import Button from "~/component/button";
import { FaChevronDown, FaChevronRight, FaTrendingUp, FaCalendar, FaFileAlt, FaArrowDown } from "react-icons/fa";
import type { CPORevenueData, CPOInvoiceDetail } from "~/app/api/finance-dashboard/route";
import CPORevenueChart from "./CPORevenueChart";

interface CPORevenueTableProps {
  cpos: CPORevenueData[];
}

const CPORevenueTable = ({ cpos }: CPORevenueTableProps) => {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const toggleRow = (cpoId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(cpoId)) {
      newExpanded.delete(cpoId);
    } else {
      newExpanded.add(cpoId);
    }
    setExpandedRows(newExpanded);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("de-DE", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  const formatMonth = (monthKey: string) => {
    const [year, month] = monthKey.split("-");
    const monthNames = [
      "Jan", "Feb", "Mär", "Apr", "Mai", "Jun",
      "Jul", "Aug", "Sep", "Okt", "Nov", "Dez"
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  const getRevenueColor = (revenue: number) => {
    if (revenue > 10000) return "text-green-600";
    if (revenue > 5000) return "text-blue-600";
    if (revenue > 1000) return "text-orange-600";
    return "text-gray-600";
  };

  if (cpos.length === 0) {
    return (
      <Card>
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">
            Keine CPO-Daten gefunden
          </p>
        </div>
      </Card>
    );
  }

  return (
    <Card header_left={
      <div className="flex items-center gap-2">
        <FaTrendingUp className="h-5 w-5" />
        CPO Revenue Details
      </div>
    }>
        <div className="space-y-2">
          {cpos.map((cpo) => (
            <div key={cpo.cpoId} className="border rounded-lg">
              {/* Main Row */}
              <div 
                className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
                onClick={() => toggleRow(cpo.cpoId)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <button className="p-1 h-6 w-6 flex items-center justify-center hover:bg-gray-100 rounded">
                      {expandedRows.has(cpo.cpoId) ? (
                        <FaChevronDown className="h-3 w-3" />
                      ) : (
                        <FaChevronRight className="h-3 w-3" />
                      )}
                    </button>
                    <div>
                      <h3 className="font-semibold text-lg">{cpo.cpoName}</h3>
                      <p className="text-sm text-gray-500">ID: {cpo.cpoId}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-6 gap-4 text-right">
                    <div>
                      <p className="text-sm text-gray-500">Gesamtumsatz</p>
                      <p className={`font-semibold ${getRevenueColor(cpo.totalRevenue)}`}>
                        {formatCurrency(cpo.totalRevenue)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Gutschriften</p>
                      <p className="font-semibold text-red-600">
                        {formatCurrency(cpo.totalCreditNotes)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Rechnungen</p>
                      <p className="font-semibold text-blue-600">
                        {formatCurrency(cpo.totalInvoiced)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Onboarding</p>
                      <p className="font-semibold text-purple-600">
                        {formatCurrency(cpo.onboardingRevenue)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Wiederkehrend</p>
                      <p className="font-semibold text-orange-600">
                        {formatCurrency(cpo.recurringRevenue)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Jahresprognose</p>
                      <p className="font-semibold text-green-600">
                        {formatCurrency(cpo.yearForecast)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Expanded Content */}
              {expandedRows.has(cpo.cpoId) && (
                <div className="border-t bg-gray-50 dark:bg-gray-800 p-4">
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 mb-3">
                      <FaCalendar className="h-4 w-4" />
                      <h4 className="font-semibold">Monatliche Aufschlüsselung</h4>
                    </div>

                    {cpo.monthlyBreakdown.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {cpo.monthlyBreakdown.slice(0, 12).map((month) => (
                          <div
                            key={month.month}
                            className="bg-white dark:bg-gray-700 p-3 rounded border"
                          >
                            <div className="flex justify-between items-center mb-2">
                              <span className="font-medium">{formatMonth(month.month)}</span>
                              <span
                                className={`text-xs px-2 py-1 rounded ${
                                  month.revenue > 0
                                    ? "bg-green-100 text-green-800"
                                    : "bg-red-100 text-red-800"
                                }`}
                              >
                                {month.revenue > 0 ? "Gewinn" : "Verlust"}
                              </span>
                            </div>
                            <div className="space-y-1 text-sm">
                              <div className="flex justify-between">
                                <span className="text-gray-600">Netto:</span>
                                <span className={month.revenue > 0 ? "text-green-600" : "text-red-600"}>
                                  {formatCurrency(month.revenue)}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Rechnungen:</span>
                                <span className="text-blue-600">
                                  {formatCurrency(month.invoiced)}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Gutschriften:</span>
                                <span className="text-red-600">
                                  {formatCurrency(month.creditNotes)}
                                </span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 text-center py-4">
                        Keine monatlichen Daten verfügbar
                      </p>
                    )}

                    {/* Revenue Breakdown */}
                    <div className="mt-6 pt-4 border-t">
                      <h4 className="font-semibold mb-3">Umsatzaufschlüsselung</h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center p-3 bg-white dark:bg-gray-700 rounded border">
                          <p className="text-sm text-gray-500">Onboarding Anteil</p>
                          <p className="text-lg font-semibold text-purple-600">
                            {cpo.totalInvoiced > 0 
                              ? `${((cpo.onboardingRevenue / cpo.totalInvoiced) * 100).toFixed(1)}%`
                              : "0%"
                            }
                          </p>
                        </div>
                        <div className="text-center p-3 bg-white dark:bg-gray-700 rounded border">
                          <p className="text-sm text-gray-500">Wiederkehrend Anteil</p>
                          <p className="text-lg font-semibold text-orange-600">
                            {cpo.totalInvoiced > 0 
                              ? `${((cpo.recurringRevenue / cpo.totalInvoiced) * 100).toFixed(1)}%`
                              : "0%"
                            }
                          </p>
                        </div>
                        <div className="text-center p-3 bg-white dark:bg-gray-700 rounded border">
                          <p className="text-sm text-gray-500">Gutschrift Rate</p>
                          <p className="text-lg font-semibold text-red-600">
                            {cpo.totalInvoiced > 0 
                              ? `${((cpo.totalCreditNotes / cpo.totalInvoiced) * 100).toFixed(1)}%`
                              : "0%"
                            }
                          </p>
                        </div>
                        <div className="text-center p-3 bg-white dark:bg-gray-700 rounded border">
                          <p className="text-sm text-gray-500">Netto Marge</p>
                          <p className="text-lg font-semibold text-green-600">
                            {cpo.totalInvoiced > 0 
                              ? `${((cpo.totalRevenue / cpo.totalInvoiced) * 100).toFixed(1)}%`
                              : "0%"
                            }
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
    </Card>
  );
};

export default CPORevenueTable;
