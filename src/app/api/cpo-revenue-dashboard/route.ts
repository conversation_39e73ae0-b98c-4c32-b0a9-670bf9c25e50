import type { NextRequest } from "next/server";
import prisma from "../../../server/db/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, StateOfInvoice, KindOfInvoice } from "@prisma/client";
import { getOusBelowOu } from "~/server/model/ou/func";
import type { CPORevenueDashboardData, CPORevenueData } from "../finance-dashboard/route";

function getMonthKey(date: Date): string {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}`;
}

function isOnboardingFee(title: string, description?: string): boolean {
  const onboardingKeywords = [
    "kartenbestellgebühr",
    "onboarding",
    "einrichtung",
    "setup",
    "aktivierung",
    "ersteinrichtung"
  ];
  
  const searchText = `${title} ${description || ""}`.toLowerCase();
  return onboardingKeywords.some(keyword => searchText.includes(keyword));
}

function calculateYearForecast(monthlyData: { month: string; revenue: number }[]): number {
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;
  
  // Filter data for current year only
  const currentYearData = monthlyData.filter(item => 
    item.month.startsWith(currentYear.toString())
  );
  
  if (currentYearData.length === 0) return 0;
  
  // Calculate average monthly revenue for current year
  const totalRevenue = currentYearData.reduce((sum, item) => sum + item.revenue, 0);
  const avgMonthlyRevenue = totalRevenue / currentYearData.length;
  
  // Forecast remaining months
  const remainingMonths = 12 - currentMonth;
  const forecastRevenue = totalRevenue + (avgMonthlyRevenue * remainingMonths);
  
  return forecastRevenue;
}

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return new Response("no auth", { status: 401 });
  }

  try {
    // Get user's OU and all OUs below it
    const userOuId = session.user.selectedOu?.id;
    let ouIds: string[] = [];

    if (userOuId) {
      const ous = await getOusBelowOu(userOuId);
      ouIds = ous.map((ou) => ou.id).filter((id) => id !== undefined && id !== null);
    }

    // Build where clause for CPO invoices
    const whereClause: any = {
      contact: {
        cpo: true, // Only CPO contacts
      },
      stateOfInvoice: {
        in: [StateOfInvoice.CREATED, StateOfInvoice.PAID], // Include both sent and paid invoices
      },
      // Exclude cancelled invoices and storno invoices
      kindOfInvoice: {
        notIn: [KindOfInvoice.STORNO, KindOfInvoice.CREDIT_STORNO],
      },
      // Exclude invoices that have been cancelled by a storno
      invoiceChilds: {
        none: {
          kindOfInvoice: {
            in: [KindOfInvoice.STORNO, KindOfInvoice.CREDIT_STORNO],
          },
        },
      },
    };

    // Only add OU filter if we have valid OU IDs
    if (ouIds.length > 0) {
      whereClause.contact.ouId = {
        in: ouIds,
      };
    }

    // Get all CPO invoices (both regular and credit notes)
    const cpoInvoices = await prisma.invoice.findMany({
      where: whereClause,
      include: {
        contact: true,
        invoicePositions: true,
        contract: true,
      },
      orderBy: {
        invoiceDate: "desc",
      },
    });

    // Group invoices by CPO contact
    const cpoMap = new Map<string, {
      contact: any;
      invoices: any[];
      creditNotes: any[];
      monthlyData: Map<string, { revenue: number; creditNotes: number; invoiced: number }>;
    }>();

    for (const invoice of cpoInvoices) {
      if (!invoice.contact) continue;

      const cpoId = invoice.contact.id;
      if (!cpoMap.has(cpoId)) {
        cpoMap.set(cpoId, {
          contact: invoice.contact,
          invoices: [],
          creditNotes: [],
          monthlyData: new Map(),
        });
      }

      const cpoData = cpoMap.get(cpoId)!;
      
      if (invoice.kindOfInvoice === KindOfInvoice.CREDIT) {
        cpoData.creditNotes.push(invoice);
      } else {
        cpoData.invoices.push(invoice);
      }

      // Add to monthly breakdown
      if (invoice.invoiceDate) {
        const monthKey = getMonthKey(invoice.invoiceDate);
        if (!cpoData.monthlyData.has(monthKey)) {
          cpoData.monthlyData.set(monthKey, { revenue: 0, creditNotes: 0, invoiced: 0 });
        }

        const monthData = cpoData.monthlyData.get(monthKey)!;
        if (invoice.kindOfInvoice === KindOfInvoice.CREDIT) {
          monthData.creditNotes += invoice.sumGross;
          // Don't subtract credit notes from revenue - they are separate transactions
        } else {
          monthData.invoiced += invoice.sumGross;
          monthData.revenue += invoice.sumGross;
        }
      }
    }

    // Process each CPO's data
    const cpos: CPORevenueData[] = [];
    let totalRevenue = 0;
    let totalCreditNotes = 0;
    let totalInvoiced = 0;
    let totalOnboarding = 0;
    let totalRecurring = 0;

    for (const [cpoId, cpoData] of cpoMap.entries()) {
      const cpoRevenue = cpoData.invoices.reduce((sum, inv) => sum + inv.sumGross, 0);
      const cpoCreditNotes = cpoData.creditNotes.reduce((sum, inv) => sum + inv.sumGross, 0);
      
      // Calculate onboarding vs recurring revenue
      let onboardingRevenue = 0;
      let recurringRevenue = 0;
      
      for (const invoice of cpoData.invoices) {
        for (const position of invoice.invoicePositions) {
          if (isOnboardingFee(position.title, position.description)) {
            onboardingRevenue += position.sumGross;
          } else {
            recurringRevenue += position.sumGross;
          }
        }
      }

      // Convert monthly data to array
      const monthlyBreakdown = Array.from(cpoData.monthlyData.entries())
        .map(([month, data]) => ({
          month,
          revenue: data.revenue,
          creditNotes: data.creditNotes,
          invoiced: data.invoiced,
        }))
        .sort((a, b) => b.month.localeCompare(a.month));

      const yearForecast = calculateYearForecast(monthlyBreakdown);
      // Don't subtract credit notes from revenue - they are separate transactions

      cpos.push({
        cpoId,
        cpoName: cpoData.contact.companyName || cpoData.contact.name || "Unbekannt",
        totalRevenue: cpoRevenue, // Only invoice revenue, not reduced by credit notes
        totalCreditNotes: cpoCreditNotes,
        totalInvoiced: cpoRevenue,
        onboardingRevenue,
        recurringRevenue,
        yearForecast,
        monthlyBreakdown,
      });

      totalRevenue += cpoRevenue; // Only invoice revenue
      totalCreditNotes += cpoCreditNotes;
      totalInvoiced += cpoRevenue;
      totalOnboarding += onboardingRevenue;
      totalRecurring += recurringRevenue;
    }

    // Sort CPOs by total revenue (descending)
    cpos.sort((a, b) => b.totalRevenue - a.totalRevenue);

    const result: CPORevenueDashboardData = {
      cpos,
      totalRevenue,
      totalCreditNotes,
      totalInvoiced,
      totalOnboarding,
      totalRecurring,
    };

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error fetching CPO revenue dashboard data:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
